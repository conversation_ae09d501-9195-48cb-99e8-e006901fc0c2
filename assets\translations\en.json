{"arabic": "Arabic", "english": "English", "confirm": "Confirm", "or": "Or", "optional": "Optional", "this_field_is_required": "This field is required", "email_is_not_valid": "Email is not valid", "phone_number_is_not_valid": "Phone number is not valid", "very_short": "Very short", "very_long": "Very long", "enter_your_number": "Enter your number", "login_using_phone_number": "login using phone number", "terms_and_conditions": "Terms & Conditions", "privacy_policy": "Privacy Policy", "verify_phone_number": "Verify phone number", "enter_the_code_sent_to": "Enter the code sent to", "did_not_receive_a_code": "Didn't receive a Code?", "resend_code": "Resend Code", "select_language": "Select Language", "select_your_language_to_continue": "Select your language to continue", "signup": "Signup", "signup_message": "Welcome to GOPAL , Please create an account to complete the login process, or you can log in as a guest ", "continue_as_a_guest": "Continue as a guest", "create_new_account": "Create New Account", "enter_your_information_to_create_new_account": "Enter your information to create new account", "name": "Name", "enter_your_name": "Enter your name", "date_of_birth": "Date of Birth", "age": "Age", "email": "Email", "add_child": "Add Child", "edit_child": "Edit Child", "accept_terms_message_1": "By Continuing you agree to", "accept_terms_message_2": "GoPal’s Term of use", "comma": ",", "accept_terms_message_3": "you may read our", "accept_terms_message_4": "privacy policy", "accept_terms_message_5": "here", "select_photo": "Select Photo", "first_name": "First Name", "enter_first_name": "Enter first name", "last_name": "Last Name", "enter_last_name": "Enter last name", "hobbies": "Hobbies", "gender": "Gender", "select_gender": "Select Gender", "gender_male": "Male", "gender_female": "Female", "medical_information": "Medical Information", "allergies": "Allergies", "spring_allergy": "Spring allergy", "special_case": "Special Case", "please_select_child_gender": "Please Select Child Gender", "home": "Home", "categories": "Categories", "subcategories": "Subcategories", "my_activities": "My Activities", "profile": "Profile", "see_all": "See All", "centers_near_you": "Centers Near You", "recommended_activities": "Recommended Activities", "seasonal_activities": "Seasonal Activities", "family_activities": "Family Activities", "available": "Available", "unavailable": "Unavailable", "available_seats": "{} of available seats", "season_expiration_days": {"one": "Season expiration: {} day", "other": "Season Expiration: {} days"}, "activity_details": "Activity Details", "pictures_related_to_the_activity": "Pictures related to the activity", "pictures_related_to_the_center": "Pictures related to the center", "activity_price": "Activity Price", "activity_date": "Activity Date", "activity_duration": "Activity Duration", "time": "Time", "target_age": "Target Age", "capacity": "Capacity", "from_to": "From {} To {}", "about_activity": "About Activity", "about_center": "About Center", "read_more": "read more", "read_less": "read less", "organizers": "Organizers", "booking": "Booking", "search_for_activities": "Search for activities", "contact_us": "Contact us", "language": "Language", "change_language": "Change Language", "notifications": "Notifications", "about_us": "About us", "help_and_support": "Help and support", "send_your_questions_and_problems": "send your questions and problems", "delete_account": "Delete account", "delete_my_account": "delete my account", "logout": "Log Out", "logout_of_your_account": "logout of your account", "edit_profile": "Edit Profile", "number": "Number", "children": "Children", "years": {"one": "One year", "two": "{} years", "other": "{} years"}, "months": {"one": "One month", "two": "{} months", "few": "{} months", "other": "{} months"}, "hello": "Hi {}", "center": "Center", "provided_activities": "Provided Activities", "activities_provided": {"one": "one activity provided", "other": "{} activities provided"}, "branches_location": "Branches Location", "reviews": "Reviews", "booked_activities": "Booked Activities", "delete": "Delete", "cancel": "Cancel", "filters": "Filters", "price_range": "Price Range", "rating": "Rating", "minimum_age": "Minimum Age", "maximum_age": "Maximum Age", "special_need": "Special Need", "apply_filters": "Apply Filters", "reset": "Reset", "child_profile": "Child Profile", "none": "None", "all": "All", "change_phone_number": "Change Phone Number", "send_message_to_admin": "Send Message To Admin", "support_subtitle": "Write your inquiry to the admin and you will be answered as soon as possible", "write_your_note": "Write your note", "no_internet": "No Internet", "validation_error": "Validation Error", "server_error": "Server Error", "forbidden": "Forbidden", "no_data_found": "No Data Found", "no_named_data_found": "No {} Found", "success": "Success", "warning": "Warning", "failure": "Failure", "distance_m_from_your_area": {"one": "one meter from your area", "other": "{} m from your area"}, "distance_km_from_your_area": "{} km from your area", "related_activities": "Related Activities", "s_a_r": "SAR", "set": "Set", "session": "Session", "per_hour": "per hour", "per_person": "per person", "per_course": "per course", "family_activity": "Family Activity", "targeted_for_families": "Targeted For Families", "season": "Season", "seats": {"one": "one seat", "other": "{} seats"}, "available_sets": "Available Sets", "open_close": "Open at {} Close at {}", "week_day": {"sunday": "Sunday", "monday": "Monday", "tuesday": "tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "suterday": "<PERSON><PERSON><PERSON>"}, "daily": "Daily", "for_days": {"few": "For {} days", "other": "For {} days"}, "days_at_week": {"one": "one day at week", "other": "{} days at week"}, "starting_from_the_date_of_joining": "starting from the date of joining", "select_one_child_at_least": "Select one child at least", "date": "Date", "select_date": "Select Date", "select_time": "Select Time", "to": "To", "delete_account_confirmation_message": "Are you sure you want to delete your account?", "logout_confirmation_message": "Are you sure you want to logout?", "all_children": "All Children", "add_session": "Add Session", "number_of_sessions": {"one": "One session", "other": "{} sessions"}, "delete_book_confirmation": "Are you sure you want to delete the book?", "cancel_book_confirmation": "Are you sure you want to cancel the book?", "booked_set": "Booked Set", "booked_session": "Booked Session", "next_session": "Next Session", "book_details": "Book Details", "certificate": "Certificate", "clicke_to_rate": "Click to rate", "rejected": "Rejected", "messages": "Messages", "support_replies": "Support Replies", "add_your_comment": "Add your comment", "your_note_added_successfully": "Your note added successfully", "price_type": "Price Type", "any": "any", "and_up": "& up", "discount": "discount", "guest_message": "<PERSON><PERSON><PERSON> login or sign up to access this feature", "delete_child_confirmation_message": "Do yuo want to delete {} account?", "pending": "Pending", "progress": "Progress", "completed": "Completed", "favourite": "Favourite", "photos": "Photos", "camera": "Camera", "document": "Document", "location": "Location", "video": "Video", "type_message": "Type Message", "file_must_be_smaller_than": "File must be smaller than {}", "you_should_enable_mic_permission_in_app_settings": "You should enable mic permission in app settings", "next": "Next", "skip": "<PERSON><PERSON>", "boarding_1": "Help your children to have fun without devices screens.", "boarding_2": "Discover and improve your children hobbies.", "boarding_3": "Create unforgettable happy family memories with them.", "activity_summary": "Activity Summary", "for_something": "For {}", "family": "Family", "seasonal": "Seasonal", "details": "Details", "sessions_number": "Sessions Number", "discover_centers": "Discover Centers", "go_to_payment": "Go To Payment", "holding": "Holding", "friend_invitation_code": "Friend invetation code", "n_points": {"zero": "No Points", "one": "One point", "other": "{} points"}, "invite_friends": "Invite friends", "your_invitation_code": "Your invitation code", "copy_the_code": "Copy the code", "invitation_message": "Invite your friends to try the app and get a reward for every invite you make", "share": "Share", "gifting_system": "Gifting System GoldPal", "redeem_points": "Redeem points", "redeem_n_points_to_m_unit": "Redeem {} points to {} {}", "chats": "Chats", "gopal_home_message": "GoPal .. Every Activity begins here.", "invoice": "Invoice", "contact_us_on_whatsapp": "Contact us on Whatsapp", "n_books": {"one": "One book", "other": "{} books"}, "search": "Search", "centers": "Centers", "activities": "Activities", "search_for_centers": "Search for centers", "todays_offers": "Today's offers", "select_persons_count": "Select Persons Count", "select_the_number_of_people_participating_in_the_activity": "Select the number of people participating in the activity.", "do_you_want_to_book_the_activity_for_you": "Do you want to book the activity for you?", "book_this_activity_and_be_one_of_the_participants": "Book this activity and be one of the participants", "access_your_current_location": "Access your current location", "to_get_perfect_experience_you_need_to_activate_the_access_to_your_location": "to get perfect experience you need to activate the access to your location", "update": "Update", "update_app": "Update App", "this_version_will_expire_at_x": "This version will expire at {}.", "download_the_new_version_now": "Download the new version now!", "book_success_msg": "We will send you a payment notification when your book is accepted by the center", "interactive_map": "Interactive Map", "you_have_x_and_you_can_redeem_y": "You have {} and you can redeem {}"}