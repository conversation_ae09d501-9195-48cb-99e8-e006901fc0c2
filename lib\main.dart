import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:feedback/feedback.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get/get.dart';
import 'package:gopal/core/localization/codegen_loader.g.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/style/style.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'core/config/app_builder.dart';
import 'core/config/defaults.dart';
import 'core/localization/localization.dart';
import 'core/routes.dart';
import 'core/services/feedback/widgets/feedback_button.dart';
import 'core/services/firebase_messaging/constants/firebase_options.dart';
import 'core/utils/file_utils.dart';
import 'features/splash_screen/index.dart';
import 'package:timeago/timeago.dart' as timeago;

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

void main() async {
  const String params = String.fromEnvironment("PRODUCTION");
  log(params);

  HttpOverrides.global = MyHttpOverrides();

  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();

  FirebaseApp app = await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  log('Initialized default app $app');

  if (kDebugMode) {
    await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(false);
  } else {
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;
    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordFlutterError(
        FlutterErrorDetails(exception: error, stack: stack),
      );
      return true;
    };
  }

  timeago.setLocaleMessages('ar', timeago.ArMessages());
  timeago.setLocaleMessages('ar_short', timeago.ArShortMessages());

  ErrorWidget.builder = (error) {
    return Material(
      child: Container(
        color: StyleRepo.red,
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Text(
            error.toString(),
            style: const TextStyle(color: StyleRepo.white),
          ),
        ),
      ),
    );
  };

  runApp(
    EasyLocalization(
      supportedLocales: AppLocalization.values.map((e) => e.locale).toList(),
      path: "assets/translations",
      assetLoader: const CodegenLoader(),
      fallbackLocale: AppLocalization.en.locale,
      child: const BetterFeedback(child: MyApp(isDev: params == "DEV")),
    ),
  );
}

class MyApp extends StatelessWidget {
  final bool isDev;
  const MyApp({super.key, required this.isDev});

  @override
  Widget build(BuildContext context) {
    Default.preferredOrientation();
    Get.put(AppBuilder(isDev: isDev));
    return Stack(
      children: [
        GetMaterialApp(
          debugShowCheckedModeBanner: false,
          title: Default.appTitle,
          theme: AppStyle.lightTheme(context.locale.languageCode == "ar"),
          themeMode: ThemeMode.light,
          localizationsDelegates: context.localizationDelegates,
          locale: context.locale,
          supportedLocales: context.supportedLocales,
          initialRoute: '/',
          unknownRoute: AppRouting.unknownRoute,
          getPages: AppRouting.routes(),
          home: const SplashScreen(),
          // home: const TestCompressingImage(),
        ),
        if (isDev) const FeedbackButton(),
      ],
    );
  }
}

class TestCompressingImage extends StatefulWidget {
  const TestCompressingImage({super.key});

  @override
  State<TestCompressingImage> createState() => _TestCompressingImageState();
}

class _TestCompressingImageState extends State<TestCompressingImage> {
  String image = "";
  String imageSize = "";

  pickImage() {
    setState(() async {
      image = (await pickAndCompressImage()) ?? "";
    });
  }

  Future<String?> pickAndCompressImage() async {
    XFile? pickedImage = await ImagePicker().pickImage(
      source: ImageSource.gallery,
    );
    if (pickedImage == null) {
      return null;
    }

    String? result = await compressAndGetFile(
      File(pickedImage.path),
      "${(await getTemporaryDirectory()).path}/${DateTime.now()}.jpg",
    );

    return result ?? pickedImage.path;
  }

  Future<String?> compressAndGetFile(File file, String targetPath) async {
    log(FileUtils.fileSize(file.lengthSync()), name: "Image Compress");
    log(targetPath, name: "Image Compress");
    XFile? result = await FlutterImageCompress.compressAndGetFile(
      file.absolute.path,
      targetPath,
      quality: 100,
      minWidth: 500,
      minHeight: 500,
      autoCorrectionAngle: false,
    );
    imageSize = FileUtils.fileSize((await result?.length())!);
    log(FileUtils.fileSize((await result?.length())!), name: "Image Compress");
    return result?.path;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: FloatingActionButton(onPressed: pickImage),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
            child:
                image.isNotEmpty
                    ? Image.file(File(image), width: 300, height: 500)
                    : const SizedBox(),
          ),
          Text(imageSize, style: const TextStyle(fontSize: 26)),
          ElevatedButton(
            onPressed: () async {
              // TODO - save image
              // File file = File(image);
              // await ImageGallerySaver.saveImage(await file.readAsBytes());
            },
            child: const Text("save"),
          ),
        ],
      ),
    );
  }
}
